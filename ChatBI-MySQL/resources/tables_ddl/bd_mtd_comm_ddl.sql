CREATE TABLE `bd_mtd_comm` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增标识符，唯一标识每条BD绩效记录',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，数据最后修改的时间戳，用于追踪数据变更',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间，数据首次插入的时间戳',
  `last_bd_name` varchar(255) DEFAULT NULL COMMENT 'BD员工姓名，当前归属的BD销售人员姓名，如：李韬、张三、王五',
  `last_bd_id` bigint DEFAULT NULL COMMENT 'BD员工ID，关联员工表的唯一标识，用于标识具体的BD人员，如：1000768',
  `dep_level3` varchar(255) DEFAULT NULL COMMENT '三级部门名称（大区），BD所属的大区组织架构，如：华中大区、华东大区、华南大区',
  `dep_name` varchar(255) DEFAULT NULL COMMENT '部门名称（区域），BD所属的具体区域部门，如：武汉、上海、深圳',
  `total_score_num` double DEFAULT NULL COMMENT '利润积分总数，BD当月累计获得的利润积分，用于绩效考核，支持高精度计算，如：2489.944917',
  `bd_performance_rate` double DEFAULT NULL COMMENT '绩效系数，BD的利润积分计算系数，影响最终佣金计算，通常为1，如：1.0000',
  `total_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '佣金总金额，BD当月获得的所有佣金总和，单位：元，如：1973.91',
  `a_commisstion_amt` decimal(10,2) DEFAULT NULL COMMENT '高价值客户总佣金，来自A类高价值客户的佣金收入，单位：元，如：1152.50',
  `a_cust_cnt` int DEFAULT NULL COMMENT '高价值客户数量，BD服务的A类高价值客户总数，如：35',
  `a_cust_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '高价值客户单客佣金，平均每个高价值客户贡献的佣金，单位：元，如：875.00',
  `more_than_spu_cnt` bigint DEFAULT NULL COMMENT '超额SPU数量，高价值客户超出基础要求的SPU商品数量，如：185',
  `a_spu_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '超额SPU佣金，因超额SPU获得的额外佣金收入，单位：元，如：277.50',
  `category_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '品类推广佣金总额，通过品类推广活动获得的佣金，单位：元，如：821.41',
  `old_cust_comm` decimal(10,2) DEFAULT NULL COMMENT '存量客户品类佣金，来自现有客户的品类推广佣金，单位：元，如：589.02',
  `new_cust_comm` decimal(10,2) DEFAULT NULL COMMENT '新增客户品类佣金，来自新开发客户的品类推广佣金，单位：元，如：232.39',
  `big_sku_cnt` double DEFAULT NULL COMMENT '大规格商品推广件数，品类推广中大规格商品的销售件数，支持高精度，如：635.554610',
  `old_big_sku_cnt` double DEFAULT NULL COMMENT '存量客户大规格推广件数，现有客户购买的大规格商品件数，如：547.070023',
  `new_big_sku_cnt` double DEFAULT NULL COMMENT '新增客户大规格推广件数，新客户购买的大规格商品件数，如：88.484587',
  `dlv_real_amt` decimal(10,2) DEFAULT NULL COMMENT 'MTD履约实付GMV，月度至今已履约订单的实际支付金额总和，单位：元，如：193732.76',
  `item_profit_amt` decimal(10,2) DEFAULT NULL COMMENT 'MTD履约商品毛利润，月度至今已履约商品的毛利润总额，单位：元，如：30810.09',
  `ds` varchar(255) DEFAULT NULL COMMENT '数据统计日期，格式YYYYMMDD，标识数据所属的统计日期，如：20250716',
  `dlv_spu_cnt` bigint DEFAULT NULL COMMENT 'MTD履约SPU数量，月度至今已履约的不同SPU商品种类数，如：360',
  `more_than_spu_cust_cnt` bigint DEFAULT NULL COMMENT '超额SPU客户数，产生超额SPU的高价值客户数量，如：31',
  PRIMARY KEY (`id`),
  KEY `idx_bd_mtd_comm_last_bd_id_a_cust_cnt` (`last_bd_id`,`a_cust_cnt`) COMMENT '按BD查询高价值客户数量的复合索引，用于客户数量排序和筛选',
  KEY `idx_bd_mtd_comm_last_bd_id_more_than_spu_cnt` (`last_bd_id`,`more_than_spu_cnt`) COMMENT '按BD查询超额SPU数量的复合索引，用于SPU业绩分析',
  KEY `idx_bd_mtd_comm_last_bd_id_a_spu_comm_amt` (`last_bd_id`,`a_spu_comm_amt`) COMMENT '按BD查询超额SPU佣金的复合索引，用于佣金排序和统计',
  KEY `idx_bd_mtd_comm_last_bd_id_dlv_real_amt` (`last_bd_id`,`dlv_real_amt`) COMMENT '按BD查询履约GMV的复合索引，用于业绩排序和GMV分析'
) ENGINE=InnoDB AUTO_INCREMENT=11024 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='BD月度至今绩效汇总表，记录每个BD销售人员的月度累计业绩数据，包括客户数量、佣金收入、品类推广、履约业绩等关键指标，用于BD绩效考核、佣金计算和业务分析。每个BD每天一条记录，支持按时间维度追踪业绩变化趋势。主要业务场景：1)BD绩效排名和考核 2)佣金计算和分配 3)客户价值分析 4)品类推广效果评估 5)GMV和利润分析';