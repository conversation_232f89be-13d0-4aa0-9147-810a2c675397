#!/usr/bin/env python3
"""
测试 base_query_processor.py 重构后的功能
验证从 CoordinatorBot 实例动态获取 agent 配置的功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.agent.base_query_processor import BaseQueryProcessor
from src.services.agent.bots.coordinator_bot import CoordinatorBot
import concurrent.futures


class TestQueryProcessor(BaseQueryProcessor):
    """测试用的查询处理器实现"""
    
    def handle_message_output(self, message: dict) -> str:
        return str(message)


def test_get_known_agents_from_coordinator():
    """测试从 CoordinatorBot 实例获取 agent 配置"""
    print("🧪 测试从 CoordinatorBot 实例获取 agent 配置...")
    
    # 创建测试用的线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_pool:
        # 创建测试处理器
        processor = TestQueryProcessor(thread_pool)
        
        # 创建 CoordinatorBot 实例
        user_info = {
            "name": "测试用户",
            "email": "<EMAIL>",
            "union_id": "test_union_id"
        }
        
        try:
            coordinator_bot = CoordinatorBot(user_info)
            
            # 测试获取 agent 配置
            known_agents = processor._get_known_agents_from_coordinator(coordinator_bot)
            
            print(f"✅ 成功获取到的 agent 配置: {known_agents}")
            
            # 验证配置是否包含预期的 agent
            expected_agents = {"sales_order_analytics", "sales_kpi_analytics", "warehouse_and_fulfillment", "general_chat_bot"}
            
            if known_agents.issubset(expected_agents) and len(known_agents) > 0:
                print("✅ agent 配置验证通过")
                return True
            else:
                print(f"❌ agent 配置验证失败，期望包含: {expected_agents}，实际获取: {known_agents}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False


def test_extract_agent_name_from_tool_call():
    """测试从工具调用内容中提取 agent 名称"""
    print("\n🧪 测试从工具调用内容中提取 agent 名称...")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_pool:
        processor = TestQueryProcessor(thread_pool)
        
        # 测试数据
        test_cases = [
            {
                "content": "Called tool: sales_order_analytics with args: {...}",
                "known_agents": {"sales_order_analytics", "warehouse_and_fulfillment", "general_chat_bot"},
                "expected": "sales_order_analytics"
            },
            {
                "content": "Called tool: warehouse_and_fulfillment with args: {...}",
                "known_agents": {"sales_order_analytics", "warehouse_and_fulfillment", "general_chat_bot"},
                "expected": "warehouse_and_fulfillment"
            },
            {
                "content": "Called tool: unknown_agent with args: {...}",
                "known_agents": {"sales_order_analytics", "warehouse_and_fulfillment", "general_chat_bot"},
                "expected": None
            }
        ]
        
        all_passed = True
        for i, test_case in enumerate(test_cases):
            result = processor._extract_agent_name_from_tool_call(
                test_case["content"], 
                test_case["known_agents"]
            )
            
            if result == test_case["expected"]:
                print(f"✅ 测试用例 {i+1} 通过: {test_case['content'][:50]}... -> {result}")
            else:
                print(f"❌ 测试用例 {i+1} 失败: 期望 {test_case['expected']}，实际 {result}")
                all_passed = False
        
        return all_passed


if __name__ == "__main__":
    print("🚀 开始测试 base_query_processor.py 重构后的功能\n")
    
    test1_passed = test_get_known_agents_from_coordinator()
    test2_passed = test_extract_agent_name_from_tool_call()
    
    print(f"\n📊 测试结果:")
    print(f"- 获取 agent 配置: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"- 提取 agent 名称: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！重构成功完成。")
        print("✨ 现在 base_query_processor.py 已经移除了硬编码的 KNOWN_AGENTS，")
        print("   改为从 CoordinatorBot 实例动态获取 agent 配置，遵循了 DRY 原则。")
    else:
        print("\n❌ 部分测试失败，请检查代码。")
        sys.exit(1)
